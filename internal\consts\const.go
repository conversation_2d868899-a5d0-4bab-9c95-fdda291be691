package consts

import (
	"time"

	"github.com/gin-gonic/gin"
)

// AccessTokenExpiresIn 登录token有效期
const AccessTokenExpiresIn = time.Minute * 30
const RefreshTokenExpiresIn = time.Hour * 24 * 7
const PhoneCodeExpiresIn = time.Minute * 5

// UserCacheExpiration 用户数据缓存过期时间
const UserCacheExpiration = 1 * 24 * 60 * time.Minute // 缓存过期时间

const AppSystemExpiresIn = 1 * 24 * 60 * time.Minute // app系统缓存过期时间

// AdminPrefix
// 管理端路由前缀
// 管理端token set 前缀
// 管理菜单类型、角色类型
const AdminPrefix string = "admin"

// AgencyPrefix
// 经销商端路由前缀
// 代理端token set 前缀
// 经销商端菜单类型、角色类型
const AgencyPrefix string = "agency"
const EndpointPrefix = "endpoint"

// 分页默认值
const (
	DefaultPage     = 1
	DefaultPageSize = 15
)

var AllRoutes gin.RoutesInfo
