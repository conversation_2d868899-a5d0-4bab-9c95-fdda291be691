package admin

import (
	"marketing/internal/dao"
	operationDao "marketing/internal/dao/operation"
	"marketing/internal/handler/admin/operation"
	operationSvc "marketing/internal/service/operation"

	"marketing/internal/pkg/redis"

	"github.com/gin-gonic/gin"
)

type OperationRouter struct{}

func NewOperationRouter() *OperationRouter {
	return &OperationRouter{}
}

func (o *OperationRouter) Register(r *gin.RouterGroup) {
	articleDao := operationDao.NewOpArticleDao()

	// 内容管理
	articleRouter := r.Group("/article")
	{
		//articleDao := dao.NewGormOpArticleDao(db.DB)
		//articleService := service.NewGormArticleService(articleDao)
		//articleController := operation.NewArticleHandle(articleService)

		// 内容
		// 操作接口
		//articleRouter.POST("/create", articleController.Create)
		//articleRouter.POST("/update", articleController.Update)
		//articleRouter.DELETE("/delete/:id", articleController.Delete)
		// 查询接口
		//articleRouter.GET("/list", articleController.List)
		//articleRouter.GET("/creators", articleController.Creators)
		//articleRouter.GET("/detail/:id", articleController.Detail)

		// 评论
		//articleRouter.GET("/:id/comment/list", articleController.CommentList)

		// 类别
		//articleRouter.GET("/category/list", articleController.CategoryList)

		// 标签
		//articleRouter.GET("/tag/list", articleController.TagList)

		articleCategoryDao := dao.NewOpArticleCategoryDao()
		articleTagDao := operationDao.NewOpArticleTagDao()
		shareInfoDao := dao.NewShareInfoDao()
		opLikeDao := operationDao.NewOpLikeDao()
		opViewDao := operationDao.NewOpViewDao()
		opCommentDao := operationDao.NewOpArticleCommentDao()
		articleService := operationSvc.NewOpArticleService(articleCategoryDao, articleTagDao, shareInfoDao, opLikeDao, opViewDao, opCommentDao, articleDao)
		articleController := operation.NewOpArticle(articleService)
		articleRouter.GET("/list", articleController.GetOpArticleList)
		articleRouter.POST("/edit", articleController.EditOpArticle)
		articleRouter.DELETE("/delete", articleController.DeleteOpArticle)
		articleRouter.PUT("/update-shareable", articleController.UpdateShareable)
		articleRouter.PUT("/update-enabled", articleController.UpdateEnabled)
		articleRouter.PUT("/update-top", articleController.UpdateTop)
	}

	articleCategoryRouter := r.Group("/article_category")
	{
		articleCategoryDao := dao.NewOpArticleCategoryDao()
		redisClient := redis.NewRedis()
		articleCategoryService := operationSvc.NewMaterialCategoryService(articleCategoryDao, redisClient)
		articleCategoryController := operation.NewOpArticleCategory(articleCategoryService)
		articleCategoryRouter.GET("/list", articleCategoryController.GetArticleCategoryList)
		articleCategoryRouter.GET("/refresh", articleCategoryController.RefreshArticleCategory)
		articleCategoryRouter.POST("/edit", articleCategoryController.EditArticleCategory)
		articleCategoryRouter.DELETE("/delete", articleCategoryController.DeleteArticleCategory)
	}

	articleTagRouter := r.Group("/article_tag")
	{
		articleTagDao := operationDao.NewOpArticleTagDao()
		articleTagService := operationSvc.NewOpArticleTagService(articleTagDao)
		articleTagController := operation.NewOpArticleTag(articleTagService)
		articleTagRouter.GET("/list", articleTagController.GetArticleTagList)
		articleTagRouter.POST("/edit", articleTagController.EditArticleTag)
		articleTagRouter.DELETE("/delete", articleTagController.DeleteArticleTag)
	}

	articleUserRouter := r.Group("/article_user")
	{
		articleUserDao := operationDao.NewOpUserDao()
		articleUserService := operationSvc.NewOpUserService(articleUserDao)
		articleUserController := operation.NewOpUser(articleUserService)
		articleUserRouter.GET("/list", articleUserController.GetOpUserList)
		articleUserRouter.GET("/statistics", articleUserController.Statistics)
		articleUserRouter.POST("/block", articleUserController.BlockOpUser)
	}

	articlePublisherRouter := r.Group("/article_publisher")
	{
		articlePublisherDao := operationDao.NewOpPublisherDao()
		articlePublisherService := operationSvc.NewOpPublisherService(articlePublisherDao)
		articleUserController := operation.NewOpPublisher(articlePublisherService)
		articlePublisherRouter.GET("/list", articleUserController.GetOpPublisherList)
		articlePublisherRouter.POST("/edit", articleUserController.EditOpPublisher)
		articlePublisherRouter.DELETE("/delete", articleUserController.DeleteOpPublisher)
	}

	articleCommentRouter := r.Group("/article_comment")
	{
		articleCommentDao := operationDao.NewOpArticleCommentDao()
		articleCommentService := operationSvc.NewOpArticleCommentService(articleDao, articleCommentDao)
		articleUserController := operation.NewOpArticleComment(articleCommentService)
		articleCommentRouter.GET("/statistics", articleUserController.GetOpArticleCommentStatistics)
		articleCommentRouter.GET("/list", articleUserController.GetOpArticleCommentList)
		articleCommentRouter.POST("/status/update", articleUserController.UpdateOpArticleCommentStatus)
		articleCommentRouter.POST("/reply", articleUserController.OpArticleCommentReply)
	}
}
