package types

import (
	"encoding/json"
	"fmt"
)

// ExampleModel 示例模型，展示如何使用OssPath类型
type ExampleModel struct {
	ID       uint    `json:"id" gorm:"primaryKey"`
	Name     string  `json:"name"`
	Avatar   OssPath `json:"avatar" gorm:"column:avatar"`        // OSS文件路径
	CoverImg OssPath `json:"cover_img" gorm:"column:cover_img"`  // OSS文件路径
}

// ExampleUsage 展示如何使用OssPath类型的示例
func ExampleUsage() {
	fmt.Println("=== OssPath类型使用示例 ===")

	// 1. 基本使用 - 设置完整URL，自动截取为相对路径
	var avatar OssPath
	avatar.Set("https://static.readboy.com/marketing/images/avatar.jpg")
	fmt.Printf("设置完整URL后的相对路径: %s\n", avatar.GetRelativePath()) // 输出: marketing/images/avatar.jpg
	fmt.Printf("获取完整URL: %s\n", avatar.GetFullUrl())                // 输出: https://static.readboy.com/marketing/images/avatar.jpg

	// 2. 设置相对路径
	avatar.Set("marketing/images/avatar2.jpg")
	fmt.Printf("设置相对路径后的值: %s\n", avatar.GetRelativePath()) // 输出: marketing/images/avatar2.jpg

	// 3. JSON序列化 - 自动输出完整URL
	var coverImg OssPath = "marketing/images/cover.jpg"
	jsonData, _ := json.Marshal(coverImg)
	fmt.Printf("JSON序列化结果: %s\n", string(jsonData)) // 输出: "https://static.readboy.com/marketing/images/cover.jpg"

	// 4. String方法 - 返回完整URL
	fmt.Printf("String方法结果: %s\n", coverImg.String()) // 输出: https://static.readboy.com/marketing/images/cover.jpg

	// 5. 在结构体中的使用示例
	model := ExampleModel{
		ID:       1,
		Name:     "测试用户",
		Avatar:   "marketing/images/user_avatar.jpg",
		CoverImg: "marketing/images/user_cover.jpg",
	}

	// JSON序列化整个结构体 - 所有OssPath字段都会自动拼接完整URL
	modelJSON, _ := json.Marshal(model)
	fmt.Printf("结构体JSON序列化结果: %s\n", string(modelJSON))

	fmt.Println("=== 示例结束 ===")
}
