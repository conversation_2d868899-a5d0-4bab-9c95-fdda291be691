package types

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/spf13/viper"
)

// JSONStringArray 自定义 JSON 字符串数组类型
type JSONStringArray []string

func (j JSONStringArray) Value() (driver.Value, error) {
	if len(j) == 0 {
		return nil, nil // 返回空 JSON 数组
	}
	bytes, err := json.Marshal(j)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil // 确保返回的是字符串
}

func (j *JSONStringArray) Scan(value interface{}) error {
	if value == nil {
		*j = JSONStringArray{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

func (j *JSONStringArray) MarshalJSON() ([]byte, error) {
	if len(*j) == 0 {
		return []byte("[]"), nil
	}
	return json.Marshal([]string(*j))
}

func (j *JSONStringArray) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		*j = make(JSONStringArray, 0)
		return nil
	}

	var s []string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*j = JSONStringArray(s)
	return nil
}

func (j *JSONStringArray) AddPrefix(prefix string) {
	for i, v := range *j {
		(*j)[i] = addPrefixIfNeeded(v, prefix)
	}
}

// AddPrefixSmart 智能添加前缀，自动判断使用哪个前缀
func (j *JSONStringArray) AddPrefixSmart() {
	for i, v := range *j {
		(*j)[i] = addSmartPrefix(v)
	}
}

func (j *JSONStringArray) Filter(fn func(string) bool) {
	var result JSONStringArray
	for _, v := range *j {
		if fn(v) {
			result = append(result, v)
		}
	}
	*j = result
}

func (j *JSONStringArray) Map(fn func(string) string) {
	for i, v := range *j {
		(*j)[i] = fn(v)
	}
}

// addPrefixIfNeeded 智能添加前缀，只对不完整的路径添加前缀
func addPrefixIfNeeded(url, prefix string) string {
	if url == "" {
		return url
	}

	// 如果已经是完整的HTTP/HTTPS URL，直接返回
	if isCompleteUrl(url) {
		return url
	}

	// 如果不是完整URL，需要智能选择前缀
	return getSmartPrefix(url) + url
}

// isCompleteUrl 检查是否是完整的URL
func isCompleteUrl(url string) bool {
	// 检查是否以 http:// 或 https:// 开头
	return len(url) > 7 && (url[:7] == "http://" || (len(url) > 8 && url[:8] == "https://"))
}

// getSmartPrefix 根据文件路径智能选择前缀
func getSmartPrefix(url string) string {
	// 如果路径以 rbcare 开头，使用旧的OSS前缀
	if len(url) >= 6 && url[:6] == "rbcare" {
		oldUrl := viper.GetString("oss.oldUrl")
		if oldUrl == "" {
			oldUrl = "https://dt1.readboy.com/"
		}
		// 确保URL以/结尾
		if oldUrl[len(oldUrl)-1] != '/' {
			oldUrl += "/"
		}
		return oldUrl
	}
	// 否则使用新的OSS前缀
	newUrl := viper.GetString("oss.url")
	if newUrl == "" {
		newUrl = "https://static.readboy.com/"
	}
	// 确保URL以/结尾
	if newUrl[len(newUrl)-1] != '/' {
		newUrl += "/"
	}
	return newUrl
}

// addSmartPrefix 智能添加前缀，自动判断使用哪个前缀
func addSmartPrefix(url string) string {
	if url == "" {
		return url
	}

	// 如果已经是完整的HTTP/HTTPS URL，直接返回
	if isCompleteUrl(url) {
		return url
	}

	// 如果不是完整URL，智能选择前缀
	return getSmartPrefix(url) + url
}
