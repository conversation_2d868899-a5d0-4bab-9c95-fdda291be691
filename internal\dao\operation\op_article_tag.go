package operation

import (
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type OpArticleTagDao interface {
	CreateOpArticleTag(c *gin.Context, name string, createdBy uint) error
	UpdateOpArticleTag(c *gin.Context, id int, name string) error
	DeleteOpArticleTag(c *gin.Context, id int) error
	GetOpArticleTagList(c *gin.Context, pageNum, pageSize int) (list []operation.OpArticleTagInfo, total int64)
	GetOpArticleTagsByAids(c *gin.Context, aids []uint) (list []model.OpArticleTagInfo)
}

// OpArticleTagDaoImpl 实现 OpArticleTagDao 接口
type OpArticleTagDaoImpl struct {
	db *gorm.DB
}

// NewOpArticleTagDao 创建 OpArticleTagDao 实例
func NewOpArticleTagDao() OpArticleTagDao {
	return &OpArticleTagDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpArticleTagDaoImpl) CreateOpArticleTag(c *gin.Context, name string, createdBy uint) error {
	return d.db.WithContext(c).Create(&model.OpArticleTag{Name: name, CreatedBy: createdBy}).Error
}

func (d *OpArticleTagDaoImpl) UpdateOpArticleTag(c *gin.Context, id int, name string) error {
	return d.db.WithContext(c).Model(&model.OpArticleTag{}).Where("id = ?", id).Update("name", name).Error
}

func (d *OpArticleTagDaoImpl) DeleteOpArticleTag(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.OpArticleTag{}, "id = ?", id).Error
}

func (d *OpArticleTagDaoImpl) GetOpArticleTagList(c *gin.Context, pageNum, pageSize int) (list []operation.OpArticleTagInfo, total int64) {
	query := d.db.WithContext(c).Model(&model.OpArticleTag{})

	data, total := utils.PaginateQueryV1(query, pageNum, pageSize, new([]*model.OpArticleTag))

	aIds := make([]uint, 0)
	for _, a := range *data {
		aIds = append(aIds, a.ID)
		list = append(list, operation.OpArticleTagInfo{
			Id:         a.ID,
			Name:       a.Name,
			UpdateTime: utils.GetTimeStr(a.UpdatedAt),
		})
	}

	var relationInfos []struct {
		TagId uint `gorm:"column:tag_id"`
		Num   int  `gorm:"column:num"`
	}

	d.db.WithContext(c).Model(&model.OpArticleTagRelation{}).
		Select("tag_id,count(*) num").
		Where("tag_id in (?)", aIds).
		Group("tag_id").
		Order("id").
		Find(&relationInfos)

	for i, l := range list {
		for _, r := range relationInfos {
			if l.Id == r.TagId {
				list[i].ArticleNum = r.Num
				break
			}
		}
	}

	return

}

func (d *OpArticleTagDaoImpl) GetOpArticleTagsByAids(c *gin.Context, aids []uint) (list []model.OpArticleTagInfo) {
	d.db.WithContext(c).Model(&model.OpArticleTagRelation{}).
		Select("article_id,tag_id,name tag_name").
		Joins("join op_article_tag on op_article_tag_relation.tag_id = op_article_tag.id").
		Where("article_id in (?)", aids).
		Find(&list)
	return
}
