package operation

import (
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/operation"

	"github.com/gin-gonic/gin"
)

type OpPublisher struct {
	svc operation.OpPublisherSvcInterface
}

func NewOpPublisher(svc operation.OpPublisherSvcInterface) *OpPublisher {
	return &OpPublisher{
		svc: svc,
	}
}

func (o *OpPublisher) GetOpPublisherList(c *gin.Context) {
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := o.svc.GetOpPublisherList(c, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (o *OpPublisher) EditOpPublisher(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	name := e.ReqParamStr(c, "name")
	avatar := e.ReqParamStr(c, "avatar")

	//截取文件路径
	avatar = utils.DeletePrefix(avatar)

	userIds := make([]int, 0)
	utils.JsonStrToObjectList(e.ReqParamStr(c, "user_ids"), &userIds)

	err := o.svc.EditOpPublisher(c, id, name, avatar, userIds)
	if err != nil {
		handler.Error(c, errors.NewErr("编辑运营号失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (o *OpPublisher) DeleteOpPublisher(c *gin.Context) {
	err := o.svc.DeleteOpPublisher(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, errors.NewErr("删除运营号失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
