package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJSONStringArray_AddPrefixSmart(t *testing.T) {
	tests := []struct {
		name     string
		input    JSONStringArray
		expected JSONStringArray
	}{
		{
			name:     "完整URL不变",
			input:    JSONStringArray{"https://example.com/image.jpg", "http://test.com/file.png"},
			expected: JSONStringArray{"https://example.com/image.jpg", "http://test.com/file.png"},
		},
		{
			name:     "rbcare路径使用旧前缀",
			input:    JSONStringArray{"rbcare/images/test.jpg", "rbcare/files/doc.pdf"},
			expected: JSONStringArray{"https://dt1.readboy.com/rbcare/images/test.jpg", "https://dt1.readboy.com/rbcare/files/doc.pdf"},
		},
		{
			name:     "普通相对路径使用新前缀",
			input:    JSONStringArray{"marketing/images/test.jpg", "uploads/file.png"},
			expected: JSONStringArray{"https://static.readboy.com/marketing/images/test.jpg", "https://static.readboy.com/uploads/file.png"},
		},
		{
			name:     "混合路径",
			input:    JSONStringArray{"https://example.com/image.jpg", "rbcare/old.jpg", "marketing/new.jpg"},
			expected: JSONStringArray{"https://example.com/image.jpg", "https://dt1.readboy.com/rbcare/old.jpg", "https://static.readboy.com/marketing/new.jpg"},
		},
		{
			name:     "空字符串",
			input:    JSONStringArray{"", "marketing/test.jpg", ""},
			expected: JSONStringArray{"", "https://static.readboy.com/marketing/test.jpg", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 复制输入以避免修改原始数据
			result := make(JSONStringArray, len(tt.input))
			copy(result, tt.input)
			
			// 执行智能前缀添加
			result.AddPrefixSmart()
			
			// 验证结果
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAddSmartPrefix(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "完整HTTPS URL",
			input:    "https://example.com/image.jpg",
			expected: "https://example.com/image.jpg",
		},
		{
			name:     "完整HTTP URL",
			input:    "http://example.com/image.jpg",
			expected: "http://example.com/image.jpg",
		},
		{
			name:     "rbcare路径",
			input:    "rbcare/images/test.jpg",
			expected: "https://dt1.readboy.com/rbcare/images/test.jpg",
		},
		{
			name:     "普通相对路径",
			input:    "marketing/images/test.jpg",
			expected: "https://static.readboy.com/marketing/images/test.jpg",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := addSmartPrefix(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsCompleteUrl(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "HTTPS URL",
			input:    "https://example.com/test.jpg",
			expected: true,
		},
		{
			name:     "HTTP URL",
			input:    "http://example.com/test.jpg",
			expected: true,
		},
		{
			name:     "相对路径",
			input:    "marketing/images/test.jpg",
			expected: false,
		},
		{
			name:     "空字符串",
			input:    "",
			expected: false,
		},
		{
			name:     "短字符串",
			input:    "test",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isCompleteUrl(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
