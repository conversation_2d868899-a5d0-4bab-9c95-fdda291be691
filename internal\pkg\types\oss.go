package types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/spf13/viper"
)

var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)

// OssPath OSS文件路径类型，自动处理相对路径存储和完整URL输出
type OssPath string

// MarshalJSON 实现 json.Marshaler 接口，输出时自动拼接完整URL
func (o OssPath) MarshalJSON() ([]byte, error) {
	if string(o) == "" {
		return []byte(`""`), nil
	}
	fullUrl := o.getFullUrl()
	return json.Marshal(fullUrl)
}

// UnmarshalJSON 实现 json.Unmarshaler 接口，输入时自动截取相对路径
func (o *OssPath) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*o = OssPath(stripOssBaseUrl(s))
	return nil
}

// Value 实现 driver.Valuer 接口，数据库存储相对路径
func (o OssPath) Value() (driver.Value, error) {
	if string(o) == "" {
		return nil, nil
	}
	return string(o), nil
}

// Scan 实现 sql.Scanner 接口，从数据库读取
func (o *OssPath) Scan(value interface{}) error {
	if value == nil {
		*o = ""
		return nil
	}

	switch v := value.(type) {
	case string:
		*o = OssPath(v)
		return nil
	case []byte:
		*o = OssPath(string(v))
		return nil
	}

	return fmt.Errorf("cannot convert %T to OssPath", value)
}

// String 实现 Stringer 接口，返回完整URL
func (o OssPath) String() string {
	if string(o) == "" {
		return ""
	}
	return o.getFullUrl()
}

// Set 设置值，自动截取相对路径
func (o *OssPath) Set(value interface{}) (old interface{}) {
	oldValue := *o
	strValue := convertToString(value)
	*o = OssPath(stripOssBaseUrl(strValue))
	return oldValue
}

// GetRelativePath 获取相对路径（数据库存储的原始值）
func (o OssPath) GetRelativePath() string {
	return string(o)
}

// GetFullUrl 获取完整URL
func (o OssPath) GetFullUrl() string {
	return o.getFullUrl()
}

// getFullUrl 内部方法，获取完整URL
func (o OssPath) getFullUrl() string {
	path := string(o)
	oldUrl := viper.GetString("oss.oldUrl")
	if strings.HasPrefix(path, "rbcare/") {
		return fillUpUrl(oldUrl, path)
	}
	newUrl := viper.GetString("oss.url")
	return fillUpUrl(newUrl, path)
}

// convertToString 将任意类型转换为字符串
func convertToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case fmt.Stringer:
		return v.String()
	default:
		return fmt.Sprintf("%v", v)
	}
}

// stripOssBaseUrl 截取OSS基础URL，保留相对路径
func stripOssBaseUrl(url string) string {
	if url == "" {
		return url
	}

	// 如果不是HTTP/HTTPS URL，直接返回
	if !httpPrefixReg.MatchString(url) {
		return url
	}

	// 获取配置中的OSS基础URL列表
	baseUrls := []string{
		viper.GetString("oss.url"),
		viper.GetString("oss.oldUrl"),
	}

	// 尝试移除各种可能的基础URL前缀
	for _, baseUrl := range baseUrls {
		if baseUrl != "" && strings.HasPrefix(url, baseUrl) {
			return strings.TrimPrefix(url, baseUrl)
		}
	}

	return url
}

// fillUpUrl 拼接完整的URL
func fillUpUrl(prefix, url string) string {
	if url == "" {
		return prefix
	}

	// 如果已经是完整的HTTP/HTTPS URL，直接返回
	if httpPrefixReg.MatchString(url) {
		return url
	}

	// 确保前缀以/结尾，URL不以/开头
	prefix = strings.TrimRight(prefix, "/")
	url = strings.TrimLeft(url, "/")

	return prefix + "/" + url
}
