package types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/spf13/viper"
)

var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)

// OssKeyReq OSS文件路径请求类型，上传时自动截取相对路径
type OssKeyReq string

// Set 实现设置方法，自动截取OSS基础URL，保留相对路径
func (k *OssKeyReq) Set(value interface{}) (old interface{}) {
	strValue := convertToString(value)
	*k = OssKeyReq(stripOssBaseUrl(strValue))
	return value
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (k OssKeyReq) Value() (driver.Value, error) {
	if string(k) == "" {
		return nil, nil
	}
	return string(k), nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (k *OssKeyReq) Scan(value interface{}) error {
	if value == nil {
		*k = ""
		return nil
	}

	switch v := value.(type) {
	case string:
		*k = OssKeyReq(v)
		return nil
	case []byte:
		*k = OssKeyReq(string(v))
		return nil
	}

	return fmt.Errorf("cannot convert %T to OssKeyReq", value)
}

// String 实现 Stringer 接口
func (k OssKeyReq) String() string {
	return string(k)
}

// OssKeyRes OSS文件路径响应类型，返回时自动拼接完整URL
type OssKeyRes string

// MarshalText 实现 encoding.TextMarshaler 接口，返回时自动拼接完整URL
func (k OssKeyRes) MarshalText() ([]byte, error) {
	baseUrl := viper.GetString("oss.url")
	if baseUrl == "" {
		baseUrl = "https://static.readboy.com/"
	}
	fullUrl := fillUpUrl(baseUrl, string(k))
	return []byte(fullUrl), nil
}

// MarshalJSON 实现 json.Marshaler 接口
func (k OssKeyRes) MarshalJSON() ([]byte, error) {
	text, err := k.MarshalText()
	if err != nil {
		return nil, err
	}
	return json.Marshal(string(text))
}

// UnmarshalJSON 实现 json.Unmarshaler 接口
func (k *OssKeyRes) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*k = OssKeyRes(s)
	return nil
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (k OssKeyRes) Value() (driver.Value, error) {
	if string(k) == "" {
		return nil, nil
	}
	return string(k), nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (k *OssKeyRes) Scan(value interface{}) error {
	if value == nil {
		*k = ""
		return nil
	}

	switch v := value.(type) {
	case string:
		*k = OssKeyRes(v)
		return nil
	case []byte:
		*k = OssKeyRes(string(v))
		return nil
	}

	return fmt.Errorf("cannot convert %T to OssKeyRes", value)
}

// String 实现 Stringer 接口，返回完整URL
func (k OssKeyRes) String() string {
	baseUrl := viper.GetString("oss.url")
	if baseUrl == "" {
		baseUrl = "https://static.readboy.com/"
	}
	return fillUpUrl(baseUrl, string(k))
}

// convertToString 将任意类型转换为字符串
func convertToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case fmt.Stringer:
		return v.String()
	default:
		return fmt.Sprintf("%v", v)
	}
}

// stripOssBaseUrl 截取OSS基础URL，保留相对路径
func stripOssBaseUrl(url string) string {
	if url == "" {
		return url
	}

	// 如果不是HTTP/HTTPS URL，直接返回
	if !httpPrefixReg.MatchString(url) {
		return url
	}

	// 获取配置中的OSS基础URL列表
	baseUrls := []string{
		viper.GetString("oss.url"),
		"https://static.readboy.com/",
		"https://dt1.readboy.com/",
	}

	// 尝试移除各种可能的基础URL前缀
	for _, baseUrl := range baseUrls {
		if baseUrl != "" && strings.HasPrefix(url, baseUrl) {
			return strings.TrimPrefix(url, baseUrl)
		}
	}

	return url
}

// fillUpUrl 拼接完整的URL
func fillUpUrl(prefix, url string) string {
	if url == "" {
		return url
	}

	// 如果已经是完整的HTTP/HTTPS URL，直接返回
	if httpPrefixReg.MatchString(url) {
		return url
	}

	// 确保前缀以/结尾，URL不以/开头
	prefix = strings.TrimRight(prefix, "/")
	url = strings.TrimLeft(url, "/")

	return prefix + "/" + url
}
