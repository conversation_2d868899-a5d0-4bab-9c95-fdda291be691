package operation

import (
	"errors"
	"marketing/internal/api/operation"
	articleDao "marketing/internal/dao/operation"

	"github.com/gin-gonic/gin"
)

type OpArticleTagSvcInterface interface {
	EditOpArticleTag(c *gin.Context, id int, name string, createdBy uint) error
	DeleteOpArticleTag(c *gin.Context, id int) error
	GetOpArticleTagList(c *gin.Context, pageNum, pageSize int) (list []operation.OpArticleTagInfo, total int64)
}

type OpArticleTagService struct {
	opArticleTagRepo articleDao.OpArticleTagDao
}

func NewOpArticleTagService(opArticleTagRepo articleDao.OpArticleTagDao) OpArticleTagSvcInterface {
	return &OpArticleTagService{
		opArticleTagRepo: opArticleTagRepo,
	}
}

func (s *OpArticleTagService) EditOpArticleTag(c *gin.Context, id int, name string, createdBy uint) error {
	if len(name) == 0 {
		return errors.New("编辑文章标签:名称为空")
	}

	if id > 0 {
		return s.opArticleTagRepo.UpdateOpArticleTag(c, id, name)
	} else {
		return s.opArticleTagRepo.CreateOpArticleTag(c, name, createdBy)
	}

}

func (s *OpArticleTagService) DeleteOpArticleTag(c *gin.Context, id int) error {
	return s.opArticleTagRepo.DeleteOpArticleTag(c, id)
}

func (s *OpArticleTagService) GetOpArticleTagList(c *gin.Context, pageNum, pageSize int) (list []operation.OpArticleTagInfo, total int64) {
	return s.opArticleTagRepo.GetOpArticleTagList(c, pageNum, pageSize)
}
